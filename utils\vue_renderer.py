"""
Vue模板渲染器
负责Vue单文件组件的渲染和HTML生成
"""
import os
import json
import logging
from django.conf import settings
from django.template.loader import get_template
from django.template.exceptions import TemplateDoesNotExist
from django.utils.safestring import mark_safe
from django.http import HttpResponse

from .vue_compiler import VueCompiler
from .vue_cache import VueTemplateCache

logger = logging.getLogger(__name__)


class VueTemplateRenderer:
    """Vue模板渲染器"""
    
    def __init__(self):
        self.compiler = VueCompiler()
        self.cache = VueTemplateCache()
        
    def render(self, request, template_name, context=None):
        """
        智能双模式Vue模板渲染

        Args:
            request: Django请求对象
            template_name: Vue模板文件名（如 'pages/product.vue'）
            context: 模板上下文数据

        Returns:
            HttpResponse或str: 渲染后的HTML内容
        """
        try:
            # if settings.DEBUG:
            #     # 开发模式：使用源文件运行时编译
            #     return self._render_development_mode(request, template_name, context)
            # else:
                # 生产模式：使用Vite构建文件
            return self._render_production_mode(request, template_name, context)

        except Exception as e:
            logger.error(f"Vue模板渲染失败: {template_name} - {str(e)}")
            # 尝试降级到开发模式
            if not settings.DEBUG:
                logger.warning(f"生产模式失败，尝试降级到开发模式: {template_name}")
                try:
                    return self._render_development_mode(request, template_name, context)
                except Exception as fallback_error:
                    logger.error(f"降级渲染也失败: {str(fallback_error)}")
            raise

    def _render_development_mode(self, request, template_name, context):
        """开发模式：使用源文件运行时编译"""
        try:
            # 获取Vue源文件路径
            source_dir = getattr(settings, 'VUE_SETTINGS', {}).get('TEMPLATES', {}).get('SOURCE_DIR')
            if not source_dir:
                source_dir = os.path.join(settings.BASE_DIR, 'frontend', 'src')

            source_template_path = os.path.join(source_dir, template_name)

            # 检查源文件是否存在
            if not os.path.exists(source_template_path):
                raise TemplateDoesNotExist(f"Vue源文件不存在: {template_name}")

            # 尝试从缓存获取编译结果
            cached_result = self.cache.get_cached_template(source_template_path)
            if cached_result:
                return self._render_with_context(cached_result, context)

            # 编译Vue模板
            compiled_result = self.compiler.compile_vue_file(source_template_path, context)

            # 缓存编译结果
            self.cache.cache_template(source_template_path, compiled_result)

            # 渲染最终HTML
            return self._render_with_context(compiled_result, context)

        except Exception as e:
            logger.error(f"开发模式渲染失败: {template_name} - {str(e)}")
            raise

    def _render_production_mode(self, request, template_name, context):
        """生产模式：使用Vite构建文件"""
        try:
            # 智能查找manifest.json文件（支持多种Vite构建输出结构）
            manifest_path = None
            possible_paths = [
                # 优先查找.vite子目录中的manifest.json（标准Vite构建）
                os.path.join(settings.BASE_DIR, 'static', 'vue', '.vite', 'manifest.json'),
                # 备选：直接在构建根目录中的manifest.json（某些Vite版本或配置）
                os.path.join(settings.BASE_DIR, 'static', 'vue', 'manifest.json'),
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    manifest_path = path
                    break

            if not manifest_path:
                error_msg = f"构建清单文件不存在。尝试的路径: {possible_paths}"
                raise FileNotFoundError(error_msg)

            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = json.load(f)

            # 尝试多种可能的键名格式来查找构建文件信息
            possible_keys = [
                template_name,  # 'pages/templates_for_caihong/index.vue'
                template_name.replace('.vue', ''),  # 'pages/templates_for_caihong/index'
                f"../virtual:vue-entry:{template_name.replace('.vue', '')}",  # 虚拟入口格式
                f"virtual:vue-entry:{template_name.replace('.vue', '')}",  # 另一种虚拟格式
                f"src/{template_name}",  # 带src前缀的格式
                f"src/{template_name.replace('.vue', '')}",  # 带src前缀无扩展名
            ]

            entry_info = None
            found_key = None

            # 遍历所有可能的键名
            for key in possible_keys:
                if key in manifest:
                    entry_info = manifest[key]
                    found_key = key
                    logger.info(f"找到构建文件，使用键名: {found_key}")
                    break

            # 如果都没找到，提供详细的错误信息
            if not entry_info:
                available_keys = list(manifest.keys())
                error_msg = (
                    f"构建文件不存在。模板: {template_name}\n"
                    f"尝试的键名: {possible_keys}\n"
                    f"manifest中可用的键名: {available_keys}"
                )
                logger.error(error_msg)
                raise TemplateDoesNotExist(error_msg)

            js_file = entry_info['file']  # 'pages/templates_for_caihong/index.js'
            css_files = entry_info.get('css', [])  # ['assets/index-f5ae8f3a.css']

            # 动态生成HTML
            return self._build_production_html(js_file, css_files, context)

        except Exception as e:
            logger.error(f"生产模式渲染失败: {template_name} - {str(e)}")
            raise

    def _build_production_html(self, js_file, css_files, context):
        """构建生产环境HTML页面"""
        try:
            # 构建CSS链接
            css_links = '\n'.join([
                f'    <link rel="stylesheet" href="{settings.STATIC_URL}vue/{css_file}">'
                for css_file in css_files
            ])

            # 序列化context数据
            context_json = json.dumps(context or {}, ensure_ascii=False, default=str)

            # 构建完整的HTML页面
            html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue页面</title>
{css_links}
</head>
<body>
    <div id="app"></div>

    <!-- Django数据注入 -->
    <script>
        window.__DJANGO_CONTEXT__ = {context_json};
    </script>

    <!-- Vue应用 -->
    <script src="{settings.STATIC_URL}vue/{js_file}"></script>
</body>
</html>"""

            return HttpResponse(html, content_type='text/html')

        except Exception as e:
            logger.error(f"生产环境HTML构建失败: {str(e)}")
            raise

    def _get_template_path(self, template_name):
        """获取Vue模板文件的完整路径"""
        vue_dir = getattr(settings, 'VUE_SETTINGS', {}).get('TEMPLATES', {}).get('VUE_DIR')
        if not vue_dir:
            vue_dir = os.path.join(settings.BASE_DIR, 'templates', 'vue')
        
        return os.path.join(vue_dir, template_name)
    
    def _render_with_context(self, compiled_result, context):
        """
        将编译后的Vue组件与Django context结合渲染
        
        Args:
            compiled_result: Vue编译结果
            context: Django上下文数据
            
        Returns:
            str: 最终的HTML内容
        """
        try:
            # 获取编译后的HTML模板和JavaScript代码
            html_template = compiled_result.get('html', '')
            javascript_code = compiled_result.get('javascript', '')
            css_code = compiled_result.get('css', '')
            
            # 序列化context数据为JSON
            context_json = json.dumps(context or {}, ensure_ascii=False, default=str)
            
            # 构建最终的HTML页面
            final_html = self._build_final_html(
                html_template=html_template,
                javascript_code=javascript_code,
                css_code=css_code,
                context_json=context_json
            )
            
            return final_html
            
        except Exception as e:
            logger.error(f"Vue模板上下文渲染失败: {str(e)}")
            raise
    
    def _build_final_html(self, html_template, javascript_code, css_code, context_json):
        """
        构建最终的HTML页面
        
        Args:
            html_template: HTML模板内容
            javascript_code: JavaScript代码
            css_code: CSS样式代码
            context_json: 序列化的context数据
            
        Returns:
            str: 完整的HTML页面
        """
        # 基础HTML结构
        base_html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue页面</title>
    
    <!-- Vue运行时 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- 样式 -->
    <style>
        /* 基础样式重置 */
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
        }}
        
        /* Vue组件样式 */
        {css_code}
    </style>
</head>
<body>
    <!-- Vue应用挂载点 -->
    <div id="vue-app">
        {html_template}
    </div>
    
    <!-- Django数据注入 -->
    <script>
        // 将Django context数据注入到全局变量
        window.__DJANGO_CONTEXT__ = {context_json};
    </script>
    
    <!-- Vue应用代码 -->
    <script>
        {javascript_code}
    </script>
</body>
</html>
        """
        
        return base_html.strip()
    
    def render_component(self, component_name, props=None):
        """
        渲染单个Vue组件（用于在HTML模板中嵌入Vue组件）
        
        Args:
            component_name: 组件名称
            props: 组件属性
            
        Returns:
            str: 组件HTML代码
        """
        try:
            component_path = f"components/{component_name}.vue"
            template_path = self._get_template_path(component_path)
            
            if not os.path.exists(template_path):
                logger.warning(f"Vue组件不存在: {component_name}")
                return f"<!-- Vue组件不存在: {component_name} -->"
            
            # 编译组件
            compiled_result = self.compiler.compile_vue_file(template_path, props)
            
            # 返回组件HTML和JavaScript
            component_html = f"""
            <div id="vue-component-{component_name}">
                {compiled_result.get('html', '')}
            </div>
            <script>
                {compiled_result.get('javascript', '')}
            </script>
            <style>
                {compiled_result.get('css', '')}
            </style>
            """
            
            return mark_safe(component_html)
            
        except Exception as e:
            logger.error(f"Vue组件渲染失败: {component_name} - {str(e)}")
            return f"<!-- Vue组件渲染失败: {component_name} -->"


# 全局Vue渲染器实例
vue_renderer = VueTemplateRenderer()